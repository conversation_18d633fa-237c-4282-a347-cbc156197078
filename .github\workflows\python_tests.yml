# The name of the action
name: Python Tests
# When the action is triggered
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

# What to do when the action is triggered
jobs:
  # A job called 'build' - arbitrary
  build:
    # Run on a Ubuntu VM
    runs-on: ubuntu-latest
    steps:
      # Checkout the GitHub repo
      - uses: actions/checkout@v2

      # Install Python 3.11
      - name: Set up Python 3.11
        uses: actions/setup-python@v2
        with:
          python-version: "3.11"

      # Pip install project dependencies
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r backend/requirements.txt

      # Move into the Django project folder (./backend) and run pytest
      - name: Test with pytest
        working-directory: ./backend
        run: pytest -vv