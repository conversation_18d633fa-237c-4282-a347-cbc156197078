@import "tailwindcss";

@layer base {
  :root {
    font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* Default theme colors */
    --color-sidebar-background: #111827;
    --color-sidebar-text: #adadad;
    --color-explorer-background: #1f2937;
    --color-explorer-item-background-hover: #3a4657;
    --color-explorer-item-text-default: #adadad;
    --color-explorer-item-text-hover: #adadad;
    --color-main-tabs-background: #1f2937;
    --color-main-tab-background-default: #19202D;
    --color-main-tab-text-default: #adadad;
    --color-main-tab-active-text: #adadad;
    --color-main-tab-active-background: #111827;
    --color-main-tab-background-hover: #19202D;
    --color-main-tab-text-hover: #adadad;
    --color-main-content-background: #111827;
    --color-main-content-text: #adadad;

    /* Editor theme colors */
    --color-editor-background: #111827;
    --color-editor-text: #adadad;
    --color-editor-selection: #3b82f620;
    --color-editor-cursor: #3b82f6;
    --color-editor-line-number: #6b7280;
    --color-editor-syntax-keyword: #a78bfa;
    --color-editor-syntax-string: #34d399;
    --color-editor-syntax-comment: #6b7280;
    --color-editor-syntax-function: #f87171;
    --color-editor-syntax-variable: #adadad;
    --caret-color: #3b82f6;
  }
}

@layer utilities {
  /* Theme-specific utility classes with custom prefix */
  .theme-sidebar-background {
    background-color: var(--color-sidebar-background);
  }

  .theme-sidebar-text {
    color: var(--color-sidebar-text);
  }

  .theme-explorer-background {
    background-color: var(--color-explorer-background);
  }

  .theme-explorer-item-background:hover {
    background-color: var(--color-explorer-item-background-hover);
  }

  .theme-explorer-item-text {
    color: var(--color-explorer-item-text-default);
  }

  .theme-explorer-item-text-hover {
    color: var(--color-explorer-item-text-hover);
  }

  .theme-main-tabs-background {
    background-color: var(--color-main-tabs-background);
  }

  .theme-main-tab-background:not([data-state="active"]) {
    background-color: var(--color-main-tab-background-default);
  }
  .theme-main-tab-text:not([data-state="active"]) {
    color: var(--color-main-tab-text-default);
  }

  .theme-main-tab-text:not([data-state="active"]):hover {
    color: var(--color-main-tab-text-hover);
  }

  .theme-main-tab-text[data-state="active"]  {
    color: var(--color-main-tab-active-text);
  }

  .theme-main-tab-background[data-state="active"] {
    background-color: var(--color-main-tab-active-background);
  }

  .theme-main-tab-background:not([data-state="active"]):hover {
    background-color: var(--color-main-tab-background-hover);
  }


  .theme-main-content-background {
    background-color: var(--color-main-content-background);
  }
  .theme-main-content-text {
    color: var(--color-main-content-text);
  }

  /* Editor theme utility classes */
  .theme-editor-background {
    background-color: var(--color-editor-background);
  }

  .theme-editor-text {
    color: var(--color-editor-text);
  }

}