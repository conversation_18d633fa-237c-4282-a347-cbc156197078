# Generated by Django 5.2.1 on 2025-05-25 09:55

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0002_note_updated_at_alter_note_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='Theme',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('type', models.CharField(choices=[('predefined', 'Predefined'), ('custom', 'Custom')], default='predefined', max_length=20)),
                ('is_default', models.BooleanField(default=False)),
                ('colors', models.JSONField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['type', 'name'],
            },
        ),
    ]
