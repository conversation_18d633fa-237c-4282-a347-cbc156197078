# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.env
.venv
venv/
ENV/

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media

# Node
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log
.npm
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# React / Frontend
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
coverage/
build/
dist/

# Docker
.docker/

# IDE
.idea/
.vscode/
.continue
*.swp
*.swo
*~

# Testing
.coverage
htmlcov/
.pytest_cache/
