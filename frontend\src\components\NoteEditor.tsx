import React, { useEffect, useRef, useCallback } from "react";
import { EditorState } from "@codemirror/state";
import { EditorView, ViewUpdate } from "@codemirror/view";
import { syntaxHighlighting } from "@codemirror/language";
import { autocompletion } from "@codemirror/autocomplete";
import { useSelector } from "react-redux";

import { baseExtensions } from "@/editor/extensions";
import { hideMarkdownSyntax } from "@/editor/plugins";
import { markdownHighlightStyle, customTheme } from "@/editor/theme";
import { createCombinedLinkCompletion } from "@/editor/linkCompletion";
import { createLinkDecorations } from "@/editor/linkDecorations";
import { RootState } from "@/store";
import { useAppDispatch } from "@/hooks";
import { openTab } from "@/store/slices/tabsSlice";
import { ContentType } from "@/types/contentTypes";


interface Props {
  initialText: string;
  onContentChange?: (content: string) => void;
  currentNoteId?: string;
  onLinkClick?: (noteId: string) => void;
}


const NoteEditor: React.FC<Props> = ({
  initialText,
  onContentChange,
  currentNoteId,
  onLinkClick
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const viewRef = useRef<EditorView | null>(null);
  const onContentChangeRef = useRef(onContentChange);

  // Get notes from Redux store
  const notesState = useSelector((state: RootState) => state.notes);
  const dispatch = useAppDispatch();

  // Keep the callback ref updated
  useEffect(() => {
    onContentChangeRef.current = onContentChange;
  }, [onContentChange]);

  // Handle link clicks
  const handleLinkClick = useCallback((noteId: string) => {
    if (onLinkClick) {
      onLinkClick(noteId);
    } else {
      // Default behavior: open note in new tab
      dispatch(openTab({
        objectType: ContentType.NOTE,
        objectID: noteId
      }));
    }
  }, [onLinkClick, dispatch]);

  // Create update listener extension (stable, doesn't change)
  const updateListener = EditorView.updateListener.of((update: ViewUpdate) => {
    if (update.docChanged && onContentChangeRef.current) {
      const newContent = update.state.doc.toString();
      onContentChangeRef.current(newContent);
    }
  });

  useEffect(() => {
    if (!editorRef.current) return;

    // Create link completion and decoration extensions
    const linkCompletion = createCombinedLinkCompletion(notesState.allNotes, currentNoteId);
    const linkDecorations = createLinkDecorations(notesState.allNotes, handleLinkClick);

    const state = EditorState.create({
      doc: initialText,
      extensions: [
        ...baseExtensions,
        syntaxHighlighting(markdownHighlightStyle),
        hideMarkdownSyntax,
        updateListener,
        customTheme,
        linkDecorations, // Add link decorations
        autocompletion({ override: [linkCompletion] }), // Add link completion
      ],
    });

    const view = new EditorView({
      state,
      parent: editorRef.current
    });

    viewRef.current = view;

    return () => {
      view.destroy();
      viewRef.current = null;
    };
  }, [notesState.allNotes, currentNoteId, handleLinkClick]); // Re-create when notes or currentNoteId changes

  // Update editor content when initialText changes (e.g., when switching notes)
  useEffect(() => {
    if (viewRef.current && viewRef.current.state.doc.toString() !== initialText) {
      const transaction = viewRef.current.state.update({
        changes: {
          from: 0,
          to: viewRef.current.state.doc.length,
          insert: initialText
        }
      });
      viewRef.current.dispatch(transaction);
    }
  }, [initialText]);

  return (
    <div className="" ref={editorRef} />
  );
};

export default NoteEditor;
