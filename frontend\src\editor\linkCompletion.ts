/**
 * CodeMirror 6 autocompletion extension for note linking
 */

import { CompletionContext, CompletionResult, CompletionSource } from "@codemirror/autocomplete";
import { Note } from "@/models/Note";
import { linkParsingService, NoteSuggestion } from "@/services/linkParsingService";

/**
 * Create a completion source for note linking
 */
export function createNoteLinkCompletion(
  allNotes: Record<string, Note>,
  currentNoteId?: string
): CompletionSource {
  return async (context: CompletionContext): Promise<CompletionResult | null> => {
    // Check if we're typing a wiki-style link [[
    const wikiLinkMatch = context.matchBefore(/\[\[([^\]]*)/);
    
    if (!wikiLinkMatch && !context.explicit) return null;
    
    const query = wikiLinkMatch ? wikiLinkMatch[1] : "";
    
    // Don't show suggestions for very short queries unless explicitly requested
    if (query.length < 1 && !context.explicit) return null;
    
    // Get note suggestions
    const suggestions = linkParsingService.searchNotesForSuggestions(
      query, 
      allNotes, 
      currentNoteId
    );
    
    if (suggestions.length === 0) return null;
    
    return {
      from: wikiLinkMatch ? wikiLinkMatch.from + 2 : context.pos, // +2 to skip "[["
      options: suggestions.map(note => ({
        label: note.title,
        type: "text",
        apply: `${note.title}]]`, // Complete the wiki link
        detail: "Note",
        info: `Link to: ${note.title}`,
        boost: note.title.toLowerCase().startsWith(query.toLowerCase()) ? 1 : 0
      })),
      validFor: /^[^\]]*$/ // Valid as long as we don't type a closing bracket
    };
  };
}

/**
 * Create a completion source for markdown-style links [text](
 */
export function createMarkdownLinkCompletion(
  allNotes: Record<string, Note>,
  currentNoteId?: string
): CompletionSource {
  return async (context: CompletionContext): Promise<CompletionResult | null> => {
    // Check if we're typing a markdown link [text](
    const markdownLinkMatch = context.matchBefore(/\[[^\]]*\]\(([^)]*)/);
    
    if (!markdownLinkMatch && !context.explicit) return null;
    
    const query = markdownLinkMatch ? markdownLinkMatch[1] : "";
    
    // Don't show suggestions for very short queries unless explicitly requested
    if (query.length < 1 && !context.explicit) return null;
    
    // Get note suggestions
    const suggestions = linkParsingService.searchNotesForSuggestions(
      query, 
      allNotes, 
      currentNoteId
    );
    
    if (suggestions.length === 0) return null;
    
    return {
      from: markdownLinkMatch ? markdownLinkMatch.from + markdownLinkMatch[0].length - query.length : context.pos,
      options: [
        // Suggest note titles for title-based links
        ...suggestions.map(note => ({
          label: note.title,
          type: "text",
          apply: `${note.title})`, // Complete with title
          detail: "Note Title",
          info: `Link to: ${note.title}`,
          boost: note.title.toLowerCase().startsWith(query.toLowerCase()) ? 1 : 0
        })),
        // Suggest note IDs for ID-based links
        ...suggestions.map(note => ({
          label: `${note.title} (ID)`,
          type: "text", 
          apply: `${note.id})`, // Complete with ID
          detail: "Note ID",
          info: `Link to: ${note.title} (using ID)`,
          boost: note.title.toLowerCase().startsWith(query.toLowerCase()) ? 0.5 : 0
        }))
      ],
      validFor: /^[^)]*$/ // Valid as long as we don't type a closing parenthesis
    };
  };
}

/**
 * Combined completion source that handles both wiki and markdown links
 */
export function createCombinedLinkCompletion(
  allNotes: Record<string, Note>,
  currentNoteId?: string
): CompletionSource {
  const wikiCompletion = createNoteLinkCompletion(allNotes, currentNoteId);
  const markdownCompletion = createMarkdownLinkCompletion(allNotes, currentNoteId);
  
  return async (context: CompletionContext): Promise<CompletionResult | null> => {
    // Try wiki-style completion first
    const wikiResult = await wikiCompletion(context);
    if (wikiResult) return wikiResult;
    
    // Try markdown-style completion
    const markdownResult = await markdownCompletion(context);
    if (markdownResult) return markdownResult;
    
    return null;
  };
}
