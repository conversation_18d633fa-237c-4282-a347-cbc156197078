from .models import Note, Theme
from .serializers import NoteSerializer, ThemeSerializer
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response

class NoteViewSet(viewsets.ModelViewSet):
    serializer_class = NoteSerializer
    queryset = Note.objects.all()

    @action(detail=False, methods=['get'])
    def search_for_linking(self, request):
        """Search notes for linking autocomplete suggestions"""
        query = request.query_params.get('q', '')
        exclude_id = request.query_params.get('exclude_id')

        if len(query) < 1:
            return Response([])

        # Filter notes by title containing the query
        notes_query = Note.objects.filter(title__icontains=query)

        # Exclude current note if specified
        if exclude_id:
            notes_query = notes_query.exclude(id=exclude_id)

        # Order by title and limit results
        notes = notes_query.order_by('title')[:10]

        # Return simplified note data for suggestions
        suggestions = [{
            'id': str(note.id),
            'title': note.title
        } for note in notes]

        return Response(suggestions)


class ThemeViewSet(viewsets.ModelViewSet):
    serializer_class = ThemeSerializer
    queryset = Theme.objects.all()

    @action(detail=False, methods=['get'])
    def predefined(self, request):
        """Get all predefined themes"""
        predefined_themes = Theme.objects.filter(type='predefined')
        serializer = self.get_serializer(predefined_themes, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def custom(self, request):
        """Get all custom themes"""
        custom_themes = Theme.objects.filter(type='custom')
        serializer = self.get_serializer(custom_themes, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def default(self, request):
        """Get the default theme"""
        try:
            default_theme = Theme.objects.get(is_default=True)
            serializer = self.get_serializer(default_theme)
            return Response(serializer.data)
        except Theme.DoesNotExist:
            return Response({"error": "No default theme found"}, status=404)